LIBRARY        VMM
EXPORTS
    VMMDLL_Initialize
    VMMDLL_InitializeEx
    VMMDLL_Close
    VMMDLL_CloseAll
    VMMDLL_MemSize
    VMMDLL_MemFree

    VMMDLL_InitializePlugins
    
    VMMDLL_ConfigGet
    VMMDLL_ConfigSet
    
    VMMDLL_VfsListU
    VMMDLL_VfsListW
    VMMDLL_VfsListBlobU
    VMMDLL_VfsList_AddFile
    VMMDLL_VfsList_AddFileW
    VMMDLL_VfsList_AddDirectory
    VMMDLL_VfsList_AddDirectoryW
    VMMDLL_VfsReadU
    VMMDLL_VfsReadW
    VMMDLL_VfsWriteU
    VMMDLL_VfsWriteW
    
    VMMDLL_UtilVfsReadFile_FromPBYTE
    VMMDLL_UtilVfsReadFile_FromQWORD
    VMMDLL_UtilVfsReadFile_FromDWORD
    VMMDLL_UtilVfsReadFile_FromBOOL
    VMMDLL_UtilVfsWriteFile_BOOL
    VMMDLL_UtilVfsWriteFile_DWORD
    
    VMMDLL_MemCallback
    VMMDLL_MemPrefetchPages
    VMMDLL_MemRead
    VMMDLL_MemReadEx
    VMMDLL_MemReadPage
    VMMDLL_MemReadScatter
    VMMDLL_MemSearch
    VMMDLL_MemVirt2Phys
    VMMDLL_MemWrite
    VMMDLL_MemWriteScatter

    VMMDLL_Scatter_Initialize
    VMMDLL_Scatter_Prepare
    VMMDLL_Scatter_PrepareEx
    VMMDLL_Scatter_PrepareWrite
    VMMDLL_Scatter_PrepareWriteEx
    VMMDLL_Scatter_Execute
    VMMDLL_Scatter_ExecuteRead
    VMMDLL_Scatter_Read
    VMMDLL_Scatter_Clear
    VMMDLL_Scatter_CloseHandle
    
    VMMDLL_PidList
    VMMDLL_PidGetFromName

    VMMDLL_Map_GetNetU
    VMMDLL_Map_GetNetW
    VMMDLL_Map_GetPfn
    VMMDLL_Map_GetPfnEx
    VMMDLL_Map_GetPool
    VMMDLL_Map_GetKObjectU
    VMMDLL_Map_GetKObjectW
    VMMDLL_Map_GetKDriverU
    VMMDLL_Map_GetKDriverW
    VMMDLL_Map_GetKDeviceU
    VMMDLL_Map_GetKDeviceW
    VMMDLL_Map_GetPhysMem
    VMMDLL_Map_GetUsersU
    VMMDLL_Map_GetUsersW
    VMMDLL_Map_GetVMU
    VMMDLL_Map_GetVMW
    VMMDLL_Map_GetServicesU
    VMMDLL_Map_GetServicesW
    VMMDLL_Map_GetPteU
    VMMDLL_Map_GetPteW
    VMMDLL_Map_GetVadU
    VMMDLL_Map_GetVadW
    VMMDLL_Map_GetVadEx
    VMMDLL_Map_GetModuleU
    VMMDLL_Map_GetModuleW
    VMMDLL_Map_GetModuleFromNameU
    VMMDLL_Map_GetModuleFromNameW
    VMMDLL_Map_GetUnloadedModuleU
    VMMDLL_Map_GetUnloadedModuleW
    VMMDLL_Map_GetEATU
    VMMDLL_Map_GetEATW
    VMMDLL_Map_GetIATU
    VMMDLL_Map_GetIATW
    VMMDLL_Map_GetHeap
    VMMDLL_Map_GetHeapAlloc
    VMMDLL_Map_GetThread
    VMMDLL_Map_GetThread_CallstackU
    VMMDLL_Map_GetThread_CallstackW
    VMMDLL_Map_GetHandleU
    VMMDLL_Map_GetHandleW
    VMMDLL_ProcessGetInformation
    VMMDLL_ProcessGetInformationAll
    VMMDLL_ProcessGetInformationString
    
    VMMDLL_ProcessGetDirectoriesU
    VMMDLL_ProcessGetDirectoriesW
    VMMDLL_ProcessGetSectionsU
    VMMDLL_ProcessGetSectionsW
    VMMDLL_ProcessGetProcAddressU
    VMMDLL_ProcessGetProcAddressW
    VMMDLL_ProcessGetModuleBaseU
    VMMDLL_ProcessGetModuleBaseW
    VMMDLL_WinGetThunkInfoIATU
    VMMDLL_WinGetThunkInfoIATW
    
    VMMDLL_WinReg_HiveList
    VMMDLL_WinReg_HiveReadEx
    VMMDLL_WinReg_HiveWrite
    VMMDLL_WinReg_EnumKeyExU
    VMMDLL_WinReg_EnumKeyExW
    VMMDLL_WinReg_EnumValueU
    VMMDLL_WinReg_EnumValueW
    VMMDLL_WinReg_QueryValueExU
    VMMDLL_WinReg_QueryValueExW

    VMMDLL_VmGetVmmHandle
    VMMDLL_VmMemTranslateGPA
    VMMDLL_VmMemRead
    VMMDLL_VmMemReadScatter
    VMMDLL_VmMemWrite
    VMMDLL_VmMemWriteScatter
    VMMDLL_VmScatterInitialize

    VMMDLL_PdbLoad
    VMMDLL_PdbSymbolName
    VMMDLL_PdbSymbolAddress
    VMMDLL_PdbTypeSize
    VMMDLL_PdbTypeChildOffset
    
    VMMDLL_ForensicFileAppend
    VMMDLL_UtilFillHexAscii
    VMMDLL_YaraSearch

    VMMDLL_Log
    VMMDLL_LogEx
    