﻿  oscompatibility.c
  vmmpyc.c
  vmmpycplugin.c
  vmmpyc_kernel.c
  vmmpyc_maps.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc.c”)
  
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpycplugin.c”)
  
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_kernel.c”)
  
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_maps.c”)
  
  vmmpyc_module.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_module.c”)
  
  vmmpyc_modulemaps.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_modulemaps.c”)
  
  vmmpyc_pdb.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_pdb.c”)
  
  vmmpyc_physicalmemory.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_physicalmemory.c”)
  
  vmmpyc_process.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_process.c”)
  
  vmmpyc_processmaps.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_processmaps.c”)
  
  vmmpyc_reghive.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_reghive.c”)
  
  vmmpyc_regkey.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_regkey.c”)
  
  vmmpyc_regmemory.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_regmemory.c”)
  
  vmmpyc_regvalue.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_regvalue.c”)
  
  vmmpyc_scattermemory.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_scattermemory.c”)
  
  vmmpyc_search.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_search.c”)
  
  vmmpyc_util.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_util.c”)
  
  vmmpyc_vfs.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_vfs.c”)
  
  vmmpyc_virtualmachine.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_virtualmachine.c”)
  
  正在生成代码...
  正在编译...
  vmmpyc_virtualmemory.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_virtualmemory.c”)
  
  vmmpyc_vmm.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_vmm.c”)
  
  vmmpyc_yara.c
C:\Users\<USER>\Desktop\无dma读写\MemProcFS-master\vmmpyc\vmmpyc.h(13,10): error C1083: 无法打开包括文件: “Python.h”: No such file or directory
  (编译源文件“vmmpyc_yara.c”)
  
  正在生成代码...
