﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Header Files\leechcore">
      <UniqueIdentifier>{464d5c38-9fa4-410f-a6d0-00e4b4c6f5f9}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="leechagent.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Midl Include="leechrpc.idl">
      <Filter>Resource Files</Filter>
    </Midl>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="leechagent.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="leechagent_procchild.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="leechagent_procparent.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="leechagent_rpc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="leechrpcserver.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="leechrpcshared.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="util.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="leechrpc_s.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="leechagent_svc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="leechagent.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="leechagent_proc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="leechagent_rpc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="leechrpc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="leechrpc_h.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\leechcore.h">
      <Filter>Header Files\leechcore</Filter>
    </ClInclude>
    <ClInclude Include="leechagent_svc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>